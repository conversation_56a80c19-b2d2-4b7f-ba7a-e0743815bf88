const express = require('express');
const router = express.Router();
const CostCenterMappingModel = require('../models/CostCenterMappingModel');
const { validate, validateBulk, schemas } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/cost-center-mappings - Get all cost center mappings
router.get('/', asyncHandler(async (req, res) => {
  const { page = 1, limit = 1000, search = '' } = req.query;

  const filters = {};
  if (search.trim()) {
    filters.search = search.trim();
  }

  const pagination = {
    limit: parseInt(limit),
    offset: (parseInt(page) - 1) * parseInt(limit)
  };

  const data = await CostCenterMappingModel.findAll(filters, pagination);
  const totalCount = await CostCenterMappingModel.count(filters);

  res.json({
    success: true,
    data: data,
    pagination: {
      currentPage: parseInt(page),
      itemsPerPage: parseInt(limit),
      totalItems: totalCount,
      totalPages: Math.ceil(totalCount / parseInt(limit))
    }
  });
}));

// GET /api/cost-center-mappings/all - Get all cost center mappings without pagination
router.get('/all', asyncHandler(async (req, res) => {
  const data = await CostCenterMappingModel.findAll();

  res.json({
    success: true,
    data: data
  });
}));

// GET /api/cost-center-mappings/object - Get all mappings as a simple object for quick lookup
router.get('/object', asyncHandler(async (req, res) => {
  const mappings = await CostCenterMappingModel.getAllMappingsAsObject();

  res.json({
    success: true,
    data: mappings
  });
}));

// GET /api/cost-center-mappings/:id - Get specific mapping by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const mapping = await CostCenterMappingModel.findById(id);

  if (!mapping) {
    return res.status(404).json({
      success: false,
      message: 'Cost center mapping not found'
    });
  }

  res.json({
    success: true,
    data: mapping
  });
}));

// POST /api/cost-center-mappings - Create new mapping
router.post('/', validate(schemas.costCenterMapping), asyncHandler(async (req, res) => {
  const { originalName, alternateName } = req.body;

  // Check if mapping already exists
  const existing = await CostCenterMappingModel.findByOriginalName(originalName);
  if (existing) {
    return res.status(409).json({
      success: false,
      message: `Mapping for cost center '${originalName}' already exists`
    });
  }

  const mapping = await CostCenterMappingModel.create({
    originalName,
    alternateName
  });

  res.status(201).json({
    success: true,
    data: mapping,
    message: 'Cost center mapping created successfully'
  });
}));

// PUT /api/cost-center-mappings/:id - Update mapping
router.put('/:id', validate(schemas.costCenterMapping), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { originalName, alternateName } = req.body;

  const existing = await CostCenterMappingModel.findById(id);
  if (!existing) {
    return res.status(404).json({
      success: false,
      message: 'Cost center mapping not found'
    });
  }

  // Check if another mapping with the same originalName exists (excluding current)
  const duplicate = await CostCenterMappingModel.findByOriginalName(originalName);
  if (duplicate && duplicate.id !== parseInt(id)) {
    return res.status(409).json({
      success: false,
      message: `Mapping for cost center '${originalName}' already exists`
    });
  }

  const mapping = await CostCenterMappingModel.update(id, {
    originalName,
    alternateName
  });

  res.json({
    success: true,
    data: mapping,
    message: 'Cost center mapping updated successfully'
  });
}));

// DELETE /api/cost-center-mappings/:id - Delete mapping
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const existing = await CostCenterMappingModel.findById(id);
  if (!existing) {
    return res.status(404).json({
      success: false,
      message: 'Cost center mapping not found'
    });
  }

  await CostCenterMappingModel.delete(id);

  res.json({
    success: true,
    message: 'Cost center mapping deleted successfully'
  });
}));

// POST /api/cost-center-mappings/bulk - Bulk create/update mappings
router.post('/bulk', validateBulk(schemas.costCenterMapping), asyncHandler(async (req, res) => {
  const mappings = req.body;

  if (mappings.length === 0) {
    return res.json({
      success: true,
      data: [],
      message: 'No mappings to process'
    });
  }

  const result = await CostCenterMappingModel.bulkUpsert(mappings);

  res.json({
    success: true,
    data: result,
    message: `Successfully processed ${result.length} cost center mappings`
  });
}));

// DELETE /api/cost-center-mappings/clear - Clear all mappings
router.delete('/clear', asyncHandler(async (req, res) => {
  const result = await CostCenterMappingModel.clearAll();

  res.json({
    success: true,
    data: result,
    message: 'All cost center mappings cleared successfully'
  });
}));

module.exports = router;
