const BaseModel = require('./BaseModel');

class CostCenterMappingModel extends BaseModel {
  constructor() {
    super('cost_center_mappings');
  }

  getSearchableFields() {
    return ['originalName', 'alternateName', 'categoryName'];
  }

  getDefaultOrderBy() {
    return 'originalName ASC';
  }

  // Find mapping by original cost center name
  findByOriginalName(originalName) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE originalName = ?`;
      this.db.get(query, [originalName], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // Get all mappings as a simple object for quick lookup
  getAllMappingsAsObject() {
    return new Promise((resolve, reject) => {
      const query = `SELECT originalName, alternateName, categoryName FROM ${this.tableName}`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const mappings = {};
          rows.forEach(row => {
            mappings[row.originalName] = {
              alternateName: row.alternateName,
              categoryName: row.categoryName || ''
            };
          });
          resolve(mappings);
        }
      });
    });
  }

  // Bulk upsert mappings
  bulkUpsert(mappings) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(mappings) || mappings.length === 0) {
        resolve([]);
        return;
      }

      const timestamp = new Date().toISOString();
      const db = this.db; // Store database reference to avoid context issues
      const tableName = this.tableName;

      // Start transaction
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        let completed = 0;
        let errors = [];
        const results = [];

        const checkCompletion = () => {
          if (completed === mappings.length) {
            if (errors.length > 0) {
              db.run('ROLLBACK');
              reject(new Error(errors.join('; ')));
            } else {
              db.run('COMMIT');
              resolve(results);
            }
          }
        };

        mappings.forEach((mapping, index) => {
          const { originalName, alternateName, categoryName = '' } = mapping;

          if (!originalName || !alternateName) {
            completed++;
            errors.push(`Invalid mapping at index ${index}: originalName and alternateName are required`);
            checkCompletion();
            return;
          }

          // Check if mapping exists
          const selectQuery = `SELECT id FROM ${tableName} WHERE originalName = ?`;
          db.get(selectQuery, [originalName], (err, existing) => {
            if (err) {
              completed++;
              errors.push(`Error checking existing mapping for ${originalName}: ${err.message}`);
              checkCompletion();
            } else if (existing) {
              // Update existing mapping
              const updateQuery = `UPDATE ${tableName} SET alternateName = ?, categoryName = ?, updatedAt = ? WHERE id = ?`;
              db.run(updateQuery, [alternateName, categoryName, timestamp, existing.id], function(updateErr) {
                completed++;
                if (updateErr) {
                  errors.push(`Error updating mapping for ${originalName}: ${updateErr.message}`);
                } else {
                  results.push({ id: existing.id, originalName, alternateName, categoryName, updatedAt: timestamp });
                }
                checkCompletion();
              });
            } else {
              // Insert new mapping
              const insertQuery = `INSERT INTO ${tableName} (originalName, alternateName, categoryName, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?)`;
              db.run(insertQuery, [originalName, alternateName, categoryName, timestamp, timestamp], function(insertErr) {
                completed++;
                if (insertErr) {
                  errors.push(`Error inserting mapping for ${originalName}: ${insertErr.message}`);
                } else {
                  results.push({ id: this.lastID, originalName, alternateName, categoryName, createdAt: timestamp, updatedAt: timestamp });
                }
                checkCompletion();
              });
            }
          });
        });
      });
    });
  }

  // Delete mapping by original name
  deleteByOriginalName(originalName) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM ${this.tableName} WHERE originalName = ?`;
      this.db.run(query, [originalName], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, deletedCount: this.changes });
        }
      });
    });
  }
}

module.exports = new CostCenterMappingModel();
