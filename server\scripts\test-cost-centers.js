const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

async function testCostCenters() {
  console.log('🔍 Testing cost center data and mappings...\n');

  try {
    // Test cost center data
    console.log('1. Testing cost center data...');
    const costCenterAll = await axios.get(`${API_BASE_URL}/cost-center/all`);
    console.log('✅ Cost center (all):', costCenterAll.data.success);
    console.log('   Records:', costCenterAll.data.data?.length || 0);
    console.log('   Total:', costCenterAll.data.total || 0);

    if (costCenterAll.data.data && costCenterAll.data.data.length > 0) {
      // Extract unique cost centers
      const costCenters = costCenterAll.data.data
        .filter(record => record.costCenter && record.costCenter.trim() !== '')
        .map(record => record.costCenter.trim());
      
      const uniqueCostCenters = [...new Set(costCenters)];
      console.log('   Unique cost centers:', uniqueCostCenters.length);
      console.log('   Cost centers:', uniqueCostCenters);
      
      // Sample records
      console.log('   Sample records:');
      costCenterAll.data.data.slice(0, 5).forEach((record, index) => {
        console.log(`     ${index + 1}. Name: ${record.name}, Cost Center: ${record.costCenter}, Month: ${record.month}`);
      });
    } else {
      console.log('❌ No cost center data found!');
    }

    // Test cost center mappings
    console.log('\n2. Testing cost center mappings...');
    
    // Test paginated endpoint
    const mappingsPaginated = await axios.get(`${API_BASE_URL}/cost-center-mappings`);
    console.log('✅ Cost center mappings (paginated):', mappingsPaginated.data.success);
    console.log('   Records:', mappingsPaginated.data.data?.length || 0);
    console.log('   Pagination:', mappingsPaginated.data.pagination);

    // Test all endpoint
    const mappingsAll = await axios.get(`${API_BASE_URL}/cost-center-mappings/all`);
    console.log('✅ Cost center mappings (all):', mappingsAll.data.success);
    console.log('   Records:', mappingsAll.data.data?.length || 0);

    if (mappingsAll.data.data && mappingsAll.data.data.length > 0) {
      console.log('   Mappings:');
      mappingsAll.data.data.forEach((mapping, index) => {
        console.log(`     ${index + 1}. ${mapping.originalName} -> ${mapping.alternateName}`);
      });
    } else {
      console.log('   No mappings found (this is normal if none have been created yet)');
    }

  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
testCostCenters();
