import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  IconButton,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Tooltip,
  Paper,
  TableContainer,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Save as SaveIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {
  loadCostCenterMappings,
  saveCostCenterMappings,
  deleteCostCenterMapping,
} from '../db';

const CostCenterMappingModal = ({ open, onClose, costCenters = [] }) => {
  const [mappings, setMappings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Load existing mappings when modal opens or cost centers change
  useEffect(() => {
    if (open && costCenters.length > 0) {
      loadMappings();
    }
  }, [open, costCenters]);

  const loadMappings = async () => {
    setLoading(true);
    setError('');
    try {
      const existingMappings = await loadCostCenterMappings();

      // Create a mapping object for quick lookup
      const mappingLookup = {};
      existingMappings.forEach(mapping => {
        mappingLookup[mapping.originalName] = mapping;
      });

      // Create mappings array with ALL cost centers from the dataset
      const allMappings = costCenters.map(costCenter => {
        const existing = mappingLookup[costCenter];
        return {
          id: existing?.id || null,
          originalName: costCenter,
          alternateName: existing?.alternateName || '',
          isNew: !existing,
          isModified: false,
        };
      });

      // Sort mappings alphabetically by original name for better UX
      allMappings.sort((a, b) => a.originalName.localeCompare(b.originalName));

      setMappings(allMappings);
    } catch (err) {
      setError('Failed to load cost center mappings');
      console.error('Error loading mappings:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAlternateNameChange = (index, value) => {
    const newMappings = [...mappings];
    newMappings[index] = {
      ...newMappings[index],
      alternateName: value,
      isModified: true,
    };
    setMappings(newMappings);
    setError('');
    setSuccess('');
  };

  const handleClearMapping = async (index) => {
    const mapping = mappings[index];

    if (mapping.id) {
      // Delete from database if it exists
      try {
        await deleteCostCenterMapping(mapping.id);
        setSuccess('Mapping cleared successfully');
      } catch (err) {
        setError('Failed to clear mapping');
        console.error('Error clearing mapping:', err);
        return;
      }
    }

    // Clear the alternate name but keep the row
    const newMappings = [...mappings];
    newMappings[index] = {
      ...newMappings[index],
      id: null,
      alternateName: '',
      isNew: true,
      isModified: false,
    };
    setMappings(newMappings);
  };

  const handleSave = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Filter out mappings that have alternate names and are modified or new
      const mappingsToSave = mappings
        .filter(mapping =>
          mapping.alternateName.trim() !== '' &&
          (mapping.isModified || mapping.isNew)
        )
        .map(mapping => ({
          originalName: mapping.originalName.trim(),
          alternateName: mapping.alternateName.trim(),
        }));

      if (mappingsToSave.length > 0) {
        await saveCostCenterMappings(mappingsToSave);
        setSuccess(`Successfully saved ${mappingsToSave.length} cost center mapping(s)`);

        // Reload mappings to get updated data
        await loadMappings();
      } else {
        setSuccess('No changes to save');
      }
    } catch (err) {
      setError('Failed to save cost center mappings');
      console.error('Error saving mappings:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  const hasChanges = mappings.some(mapping =>
    mapping.isModified && mapping.alternateName.trim() !== ''
  );

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        component: motion.div,
        initial: { opacity: 0, scale: 0.9 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.9 },
        transition: { duration: 0.2 },
        sx: {
          height: '90vh', // Use 90% of viewport height
          maxHeight: '90vh',
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Cost Center Mappings {costCenters.length > 0 && `(${costCenters.length} total)`}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflow: 'hidden',
        p: 3,
      }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Configure alternate names for cost centers from your entire dataset. These alternate names will be displayed in the Tally report while preserving the original data. Leave alternate name fields empty if no mapping is desired for that cost center.
        </Typography>

        {!loading && mappings.length > 0 && (
          <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Summary:</strong> {mappings.length} total cost centers, {mappings.filter(m => m.alternateName.trim() !== '').length} with alternate names configured
            </Typography>
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : mappings.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              No cost centers available for mapping.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Please ensure that cost center data has been imported and contains valid cost center information.
            </Typography>
          </Box>
        ) : (
          <TableContainer
            component={Paper}
            sx={{
              flex: 1, // Take up remaining space
              overflow: 'auto', // Enable scrolling
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: '#c1c1c1',
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: '#a8a8a8',
                },
              },
            }}
          >
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', width: '35%' }}>Original Cost Center</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: '55%' }}>Alternate Name</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: '10%', textAlign: 'center' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mappings.map((mapping, index) => (
                  <TableRow
                    key={`${mapping.originalName}-${index}`}
                    sx={{
                      backgroundColor: mapping.isModified
                        ? 'rgba(25, 118, 210, 0.04)'
                        : mapping.alternateName.trim() !== ''
                          ? 'rgba(76, 175, 80, 0.04)'
                          : 'inherit',
                    }}
                  >
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {mapping.originalName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        value={mapping.alternateName}
                        onChange={(e) => handleAlternateNameChange(index, e.target.value)}
                        placeholder="Enter alternate name"
                        fullWidth
                      />
                    </TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>
                      <Tooltip title={mapping.alternateName ? "Clear alternate name" : "No mapping to clear"}>
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => handleClearMapping(index)}
                            color="error"
                            disabled={!mapping.alternateName}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={saving}>
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={!hasChanges || saving}
          startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CostCenterMappingModal;
